<template>
  <div class="test-container">
    <h2>Chart7 滚动测试</h2>
    <div class="controls">
      <button @click="addTestData">添加测试数据</button>
      <button @click="clearData">清空数据</button>
      <button @click="toggleScrollSpeed">切换滚动速度</button>
      <span>当前速度: {{ scrollSpeed }}px/帧</span>
    </div>
    
    <div class="chart-wrapper">
      <Chart7 :rankingData="testData" ref="chart7"></Chart7>
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <p>数据数量: {{ testData.length }}</p>
      <p>当前滚动位置: {{ currentScrollPosition }}</p>
      <p>总高度: {{ totalItemsHeight }}</p>
      <p>是否手动滚动: {{ isManualScrolling }}</p>
    </div>
  </div>
</template>

<script>
import Chart7 from './Chart7.vue'

export default {
  name: 'Chart7Test',
  components: {
    Chart7
  },
  data() {
    return {
      testData: [],
      scrollSpeed: 1
    }
  },
  computed: {
    currentScrollPosition() {
      return this.$refs.chart7?.currentScrollPosition || 0
    },
    totalItemsHeight() {
      return this.$refs.chart7?.totalItemsHeight || 0
    },
    isManualScrolling() {
      return this.$refs.chart7?.isManualScrolling || false
    }
  },
  mounted() {
    this.addTestData()
  },
  methods: {
    addTestData() {
      const names = [
        '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
        '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六', '蒋十七', '沈十八',
        '韩十九', '杨二十', '朱二一', '秦二二', '尤二三', '许二四', '何二五', '吕二六',
        '施二七', '张二八', '孔二九', '曹三十'
      ]
      
      this.testData = names.map((name, index) => ({
        id: index + 1,
        name: name,
        gender: Math.random() > 0.5 ? '男' : '女',
        level: 'district',
        district: `片区${Math.floor(index / 5) + 1}`,
        mobile_phone: `138${String(index).padStart(8, '0')}`,
        party: index % 3 === 0 ? '中共党员' : '群众',
        nationality: '汉族',
        has_avatar: Math.random() > 0.3,
        total: Math.floor(Math.random() * 100)
      }))
    },
    
    clearData() {
      this.testData = []
    },
    
    toggleScrollSpeed() {
      const speeds = [0.5, 1, 2, 3]
      const currentIndex = speeds.indexOf(this.scrollSpeed)
      const nextIndex = (currentIndex + 1) % speeds.length
      this.scrollSpeed = speeds[nextIndex]
      
      if (this.$refs.chart7) {
        this.$refs.chart7.scrollSpeed = this.scrollSpeed
      }
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  background: #1a1a2e;
  color: white;
  min-height: 100vh;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.controls button {
  padding: 8px 16px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: #357abd;
}

.chart-wrapper {
  height: 400px;
  border: 2px solid #4a90e2;
  border-radius: 8px;
  margin-bottom: 20px;
}

.debug-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
}

.debug-info h3 {
  margin-top: 0;
  color: #4a90e2;
}

.debug-info p {
  margin: 5px 0;
}
</style>
